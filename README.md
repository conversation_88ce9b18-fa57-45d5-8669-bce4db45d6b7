# MySQL到PostgreSQL数据库迁移工具

这是一个用Go语言编写的数据库迁移工具，可以将MySQL数据库完整迁移到PostgreSQL数据库。

## 功能特性

- ✅ **完整的表结构迁移**：包括字段类型、约束、索引等
- ✅ **智能数据类型映射**：自动将MySQL数据类型转换为对应的PostgreSQL类型
- ✅ **批量数据迁移**：支持大数据量的高效迁移
- ✅ **事务处理**：确保数据一致性和完整性
- ✅ **进度监控**：实时显示迁移进度
- ✅ **详细报告**：生成完整的迁移报告
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **灵活配置**：支持配置文件和命令行参数

## 数据类型映射

| MySQL类型 | PostgreSQL类型 |
|-----------|----------------|
| TINYINT | SMALLINT |
| SMALLINT | SMALLINT |
| MEDIUMINT | INTEGER |
| INT/INTEGER | INTEGER |
| BIGINT | BIGINT |
| FLOAT | REAL |
| DOUBLE | DOUBLE PRECISION |
| DECIMAL/NUMERIC | DECIMAL/NUMERIC |
| CHAR | CHAR |
| VARCHAR | VARCHAR |
| TEXT/TINYTEXT/MEDIUMTEXT/LONGTEXT | TEXT |
| BINARY/VARBINARY/BLOB | BYTEA |
| DATE | DATE |
| TIME | TIME |
| DATETIME | TIMESTAMP |
| TIMESTAMP | TIMESTAMP |
| JSON | JSONB |
| ENUM/SET | VARCHAR |

## 安装和使用

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置数据库连接

#### 方法一：使用配置文件

编辑 `config.json` 文件：

```json
{
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "your_mysql_password",
    "database": "source_database"
  },
  "postgresql": {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "your_postgres_password",
    "database": "target_database",
    "sslmode": "disable"
  },
  "batch_size": 1000
}
```

#### 方法二：使用命令行参数

```bash
go run mysql_to_postgres_migrator.go \
  -mysql-host=localhost \
  -mysql-port=3306 \
  -mysql-user=root \
  -mysql-password=password \
  -mysql-database=source_db \
  -pg-host=localhost \
  -pg-port=5432 \
  -pg-user=postgres \
  -pg-password=password \
  -pg-database=target_db \
  -batch-size=1000
```

### 3. 运行迁移

```bash
# 使用配置文件
go run mysql_to_postgres_migrator.go

# 使用自定义配置文件
go run mysql_to_postgres_migrator.go -config=my_config.json

# 使用命令行参数
go run mysql_to_postgres_migrator.go -mysql-host=************* -mysql-user=root ...
```

### 4. 编译为可执行文件

```bash
go build -o migrator mysql_to_postgres_migrator.go
./migrator -config=config.json
```

## 命令行参数

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `-config` | 配置文件路径 | config.json |
| `-mysql-host` | MySQL主机地址 | - |
| `-mysql-port` | MySQL端口 | 3306 |
| `-mysql-user` | MySQL用户名 | - |
| `-mysql-password` | MySQL密码 | - |
| `-mysql-database` | MySQL数据库名 | - |
| `-pg-host` | PostgreSQL主机地址 | - |
| `-pg-port` | PostgreSQL端口 | 5432 |
| `-pg-user` | PostgreSQL用户名 | - |
| `-pg-password` | PostgreSQL密码 | - |
| `-pg-database` | PostgreSQL数据库名 | - |
| `-batch-size` | 批量插入大小 | 1000 |

## 迁移报告

工具会生成详细的迁移报告，包括：

- 迁移开始和结束时间
- 总耗时
- 成功/失败的表数量
- 迁移的总记录数
- 错误详情
- 成功率统计

报告会同时显示在控制台和保存为JSON文件（格式：`migration_report_YYYYMMDD_HHMMSS.json`）。

## 注意事项

1. **备份数据**：在执行迁移前，请务必备份源数据库和目标数据库
2. **权限要求**：确保MySQL和PostgreSQL用户具有足够的读写权限
3. **网络连接**：确保网络连接稳定，特别是对于大数据量迁移
4. **磁盘空间**：确保目标数据库有足够的磁盘空间
5. **字符编码**：建议使用UTF-8编码避免字符问题

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **权限错误**
   - 确保用户有CREATE、INSERT、SELECT权限
   - 检查数据库访问权限配置

3. **数据类型不兼容**
   - 查看迁移报告中的错误详情
   - 手动调整特殊数据类型

4. **内存不足**
   - 减小batch_size参数
   - 增加系统内存或使用更小的批次

## 许可证

MIT License
