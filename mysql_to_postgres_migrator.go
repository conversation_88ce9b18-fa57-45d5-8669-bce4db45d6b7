package main

import (
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
)

// Config 配置结构体
type Config struct {
	MySQL struct {
		Host     string `json:"host"`
		Port     int    `json:"port"`
		User     string `json:"user"`
		Password string `json:"password"`
		Database string `json:"database"`
	} `json:"mysql"`
	PostgreSQL struct {
		Host     string `json:"host"`
		Port     int    `json:"port"`
		User     string `json:"user"`
		Password string `json:"password"`
		Database string `json:"database"`
		SSLMode  string `json:"sslmode"`
	} `json:"postgresql"`
}

// TableInfo 表信息结构体
type TableInfo struct {
	Name    string
	Columns []ColumnInfo
	Indexes []IndexInfo
}

// ColumnInfo 列信息结构体
type ColumnInfo struct {
	Name         string
	DataType     string
	IsNullable   bool
	DefaultValue *string
	IsPrimaryKey bool
	IsAutoIncr   bool
	MaxLength    *int
}

// IndexInfo 索引信息结构体
type IndexInfo struct {
	Name     string
	Columns  []string
	IsUnique bool
}

// MigrationReport 迁移报告结构体
type MigrationReport struct {
	StartTime     time.Time
	EndTime       time.Time
	TotalTables   int
	SuccessTables int
	FailedTables  []string
	TotalRecords  int64
	Errors        []string
}

// Migrator 迁移器结构体
type Migrator struct {
	mysqlDB    *sql.DB
	postgresDB *sql.DB
	config     *Config
	report     *MigrationReport
}

// MySQL到PostgreSQL数据类型映射表
var dataTypeMapping = map[string]string{
	"tinyint":    "SMALLINT",
	"smallint":   "SMALLINT",
	"mediumint":  "INTEGER",
	"int":        "INTEGER",
	"integer":    "INTEGER",
	"bigint":     "BIGINT",
	"float":      "REAL",
	"double":     "DOUBLE PRECISION",
	"decimal":    "DECIMAL",
	"numeric":    "NUMERIC",
	"char":       "CHAR",
	"varchar":    "VARCHAR",
	"text":       "TEXT",
	"tinytext":   "TEXT",
	"mediumtext": "TEXT",
	"longtext":   "TEXT",
	"binary":     "BYTEA",
	"varbinary":  "BYTEA",
	"blob":       "BYTEA",
	"tinyblob":   "BYTEA",
	"mediumblob": "BYTEA",
	"longblob":   "BYTEA",
	"date":       "DATE",
	"time":       "TIME",
	"datetime":   "TIMESTAMP",
	"timestamp":  "TIMESTAMP",
	"year":       "INTEGER",
	"json":       "JSONB",
	"enum":       "VARCHAR",
	"set":        "VARCHAR",
}

func main() {
	var configFile = flag.String("config", "config.json", "配置文件路径")
	var mysqlHost = flag.String("mysql-host", "", "MySQL主机地址")
	var mysqlPort = flag.Int("mysql-port", 3306, "MySQL端口")
	var mysqlUser = flag.String("mysql-user", "", "MySQL用户名")
	var mysqlPassword = flag.String("mysql-password", "", "MySQL密码")
	var mysqlDatabase = flag.String("mysql-database", "", "MySQL数据库名")
	var pgHost = flag.String("pg-host", "", "PostgreSQL主机地址")
	var pgPort = flag.Int("pg-port", 5432, "PostgreSQL端口")
	var pgUser = flag.String("pg-user", "", "PostgreSQL用户名")
	var pgPassword = flag.String("pg-password", "", "PostgreSQL密码")
	var pgDatabase = flag.String("pg-database", "", "PostgreSQL数据库名")

	flag.Parse()

	// 加载配置
	config, err := loadConfig(*configFile, *mysqlHost, *mysqlPort, *mysqlUser, *mysqlPassword, *mysqlDatabase,
		*pgHost, *pgPort, *pgUser, *pgPassword, *pgDatabase)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 创建迁移器
	migrator, err := NewMigrator(config)
	if err != nil {
		log.Fatalf("创建迁移器失败: %v", err)
	}
	defer migrator.Close()

	// 执行迁移
	fmt.Println("开始数据库迁移...")
	err = migrator.Migrate()
	if err != nil {
		log.Fatalf("迁移失败: %v", err)
	}

	// 生成报告
	migrator.GenerateReport()
}

// loadConfig 加载配置
func loadConfig(configFile, mysqlHost string, mysqlPort int, mysqlUser, mysqlPassword, mysqlDatabase,
	pgHost string, pgPort int, pgUser, pgPassword, pgDatabase string) (*Config, error) {

	config := &Config{}

	// 如果配置文件存在，先加载配置文件
	if _, err := os.Stat(configFile); err == nil {
		file, err := os.Open(configFile)
		if err != nil {
			return nil, fmt.Errorf("打开配置文件失败: %v", err)
		}
		defer file.Close()

		decoder := json.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
	}

	// 命令行参数覆盖配置文件
	if mysqlHost != "" {
		config.MySQL.Host = mysqlHost
	}
	if mysqlPort != 3306 {
		config.MySQL.Port = mysqlPort
	}
	if mysqlUser != "" {
		config.MySQL.User = mysqlUser
	}
	if mysqlPassword != "" {
		config.MySQL.Password = mysqlPassword
	}
	if mysqlDatabase != "" {
		config.MySQL.Database = mysqlDatabase
	}
	if pgHost != "" {
		config.PostgreSQL.Host = pgHost
	}
	if pgPort != 5432 {
		config.PostgreSQL.Port = pgPort
	}
	if pgUser != "" {
		config.PostgreSQL.User = pgUser
	}
	if pgPassword != "" {
		config.PostgreSQL.Password = pgPassword
	}
	if pgDatabase != "" {
		config.PostgreSQL.Database = pgDatabase
	}

	// 设置默认值
	if config.PostgreSQL.SSLMode == "" {
		config.PostgreSQL.SSLMode = "disable"
	}

	return config, nil
}

// NewMigrator 创建新的迁移器
func NewMigrator(config *Config) (*Migrator, error) {
	// 连接MySQL
	mysqlDSN := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		config.MySQL.User, config.MySQL.Password, config.MySQL.Host, config.MySQL.Port, config.MySQL.Database)
	mysqlDB, err := sql.Open("mysql", mysqlDSN)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %v", err)
	}

	if err := mysqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("MySQL连接测试失败: %v", err)
	}

	// 连接PostgreSQL
	pgDSN := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		config.PostgreSQL.Host, config.PostgreSQL.Port, config.PostgreSQL.User,
		config.PostgreSQL.Password, config.PostgreSQL.Database, config.PostgreSQL.SSLMode)
	postgresDB, err := sql.Open("postgres", pgDSN)
	if err != nil {
		return nil, fmt.Errorf("连接PostgreSQL失败: %v", err)
	}

	if err := postgresDB.Ping(); err != nil {
		return nil, fmt.Errorf("PostgreSQL连接测试失败: %v", err)
	}

	fmt.Println("数据库连接成功")

	return &Migrator{
		mysqlDB:    mysqlDB,
		postgresDB: postgresDB,
		config:     config,
		report: &MigrationReport{
			StartTime: time.Now(),
			Errors:    make([]string, 0),
		},
	}, nil
}

// Close 关闭数据库连接
func (m *Migrator) Close() {
	if m.mysqlDB != nil {
		m.mysqlDB.Close()
	}
	if m.postgresDB != nil {
		m.postgresDB.Close()
	}
}

// Migrate 执行迁移
func (m *Migrator) Migrate() error {
	// 获取所有表
	tables, err := m.getTables()
	if err != nil {
		return fmt.Errorf("获取表列表失败: %v", err)
	}

	m.report.TotalTables = len(tables)
	fmt.Printf("发现 %d 个表需要迁移\n", len(tables))

	// 迁移每个表
	for _, tableName := range tables {
		fmt.Printf("正在迁移表: %s\n", tableName)

		// 获取表结构
		tableInfo, err := m.getTableInfo(tableName)
		if err != nil {
			m.addError(fmt.Sprintf("获取表 %s 结构失败: %v", tableName, err))
			continue
		}

		// 创建表
		if err := m.createTable(tableInfo); err != nil {
			m.addError(fmt.Sprintf("创建表 %s 失败: %v", tableName, err))
			continue
		}

		// 迁移数据
		recordCount, err := m.migrateTableData(tableInfo)
		if err != nil {
			m.addError(fmt.Sprintf("迁移表 %s 数据失败: %v", tableName, err))
			continue
		}

		// 创建索引
		if err := m.createIndexes(tableInfo); err != nil {
			m.addError(fmt.Sprintf("创建表 %s 索引失败: %v", tableName, err))
			// 索引创建失败不算致命错误，继续处理
		}

		m.report.SuccessTables++
		m.report.TotalRecords += recordCount
		fmt.Printf("表 %s 迁移完成，迁移了 %d 条记录\n", tableName, recordCount)
	}

	m.report.EndTime = time.Now()
	return nil
}

// getTables 获取所有表名
func (m *Migrator) getTables() ([]string, error) {
	query := "SHOW TABLES"
	rows, err := m.mysqlDB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, err
		}
		tables = append(tables, tableName)
	}

	return tables, nil
}

// getTableInfo 获取表结构信息
func (m *Migrator) getTableInfo(tableName string) (*TableInfo, error) {
	tableInfo := &TableInfo{
		Name:    tableName,
		Columns: make([]ColumnInfo, 0),
		Indexes: make([]IndexInfo, 0),
	}

	// 获取列信息
	columns, err := m.getColumns(tableName)
	if err != nil {
		return nil, err
	}
	tableInfo.Columns = columns

	// 获取索引信息
	indexes, err := m.getIndexes(tableName)
	if err != nil {
		return nil, err
	}
	tableInfo.Indexes = indexes

	return tableInfo, nil
}

// getColumns 获取表的列信息
func (m *Migrator) getColumns(tableName string) ([]ColumnInfo, error) {
	query := `
		SELECT
			COLUMN_NAME,
			DATA_TYPE,
			IS_NULLABLE,
			COLUMN_DEFAULT,
			COLUMN_KEY,
			EXTRA,
			CHARACTER_MAXIMUM_LENGTH
		FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
		ORDER BY ORDINAL_POSITION
	`

	rows, err := m.mysqlDB.Query(query, m.config.MySQL.Database, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []ColumnInfo
	for rows.Next() {
		var col ColumnInfo
		var defaultValue, columnKey, extra sql.NullString
		var maxLength sql.NullInt64
		var isNullable string

		err := rows.Scan(&col.Name, &col.DataType, &isNullable, &defaultValue,
			&columnKey, &extra, &maxLength)
		if err != nil {
			return nil, err
		}

		col.IsNullable = isNullable == "YES"
		if defaultValue.Valid {
			col.DefaultValue = &defaultValue.String
		}
		col.IsPrimaryKey = columnKey.String == "PRI"
		col.IsAutoIncr = strings.Contains(extra.String, "auto_increment")
		if maxLength.Valid {
			length := int(maxLength.Int64)
			col.MaxLength = &length
		}

		columns = append(columns, col)
	}

	return columns, nil
}

// getIndexes 获取表的索引信息
func (m *Migrator) getIndexes(tableName string) ([]IndexInfo, error) {
	query := `
		SELECT
			INDEX_NAME,
			COLUMN_NAME,
			NON_UNIQUE
		FROM INFORMATION_SCHEMA.STATISTICS
		WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
		AND INDEX_NAME != 'PRIMARY'
		ORDER BY INDEX_NAME, SEQ_IN_INDEX
	`

	rows, err := m.mysqlDB.Query(query, m.config.MySQL.Database, tableName)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	indexMap := make(map[string]*IndexInfo)
	for rows.Next() {
		var indexName, columnName string
		var nonUnique int

		err := rows.Scan(&indexName, &columnName, &nonUnique)
		if err != nil {
			return nil, err
		}

		if index, exists := indexMap[indexName]; exists {
			index.Columns = append(index.Columns, columnName)
		} else {
			indexMap[indexName] = &IndexInfo{
				Name:     indexName,
				Columns:  []string{columnName},
				IsUnique: nonUnique == 0,
			}
		}
	}

	var indexes []IndexInfo
	for _, index := range indexMap {
		indexes = append(indexes, *index)
	}

	return indexes, nil
}

// createTable 创建PostgreSQL表
func (m *Migrator) createTable(tableInfo *TableInfo) error {
	// 构建CREATE TABLE语句
	var createSQL strings.Builder
	createSQL.WriteString(fmt.Sprintf("CREATE TABLE IF NOT EXISTS %s (\n", tableInfo.Name))

	var columnDefs []string
	var primaryKeys []string

	for _, col := range tableInfo.Columns {
		colDef := m.buildColumnDefinition(col)
		columnDefs = append(columnDefs, colDef)

		// 对于自增主键，SERIAL类型已经自动包含PRIMARY KEY，不需要额外添加
		if col.IsPrimaryKey && !col.IsAutoIncr {
			primaryKeys = append(primaryKeys, col.Name)
		}
	}

	createSQL.WriteString(strings.Join(columnDefs, ",\n"))

	// 添加主键约束（只对非自增主键）
	if len(primaryKeys) > 0 {
		createSQL.WriteString(",\n")
		createSQL.WriteString(fmt.Sprintf("PRIMARY KEY (%s)", strings.Join(primaryKeys, ", ")))
	}

	createSQL.WriteString("\n);")

	// 执行创建表语句
	_, err := m.postgresDB.Exec(createSQL.String())
	if err != nil {
		return fmt.Errorf("创建表失败: %v\nSQL: %s", err, createSQL.String())
	}

	return nil
}

// buildColumnDefinition 构建列定义
func (m *Migrator) buildColumnDefinition(col ColumnInfo) string {
	var def strings.Builder
	def.WriteString(fmt.Sprintf("  %s ", col.Name))

	// 如果是自增主键，使用PostgreSQL的SERIAL类型
	if col.IsAutoIncr && col.IsPrimaryKey {
		// 根据数据类型选择合适的SERIAL类型
		baseType := strings.ToLower(col.DataType)
		switch baseType {
		case "bigint":
			def.WriteString("BIGSERIAL")
		case "smallint":
			def.WriteString("SMALLSERIAL")
		default:
			def.WriteString("SERIAL")
		}
	} else {
		// 数据类型转换
		pgType := m.convertDataType(col.DataType, col.MaxLength)
		def.WriteString(pgType)
	}

	// NOT NULL约束
	if !col.IsNullable {
		def.WriteString(" NOT NULL")
	}

	// 默认值（自增字段不需要默认值）
	if !col.IsAutoIncr && col.DefaultValue != nil && *col.DefaultValue != "" {
		defaultVal := m.convertDefaultValue(*col.DefaultValue, col.DataType)
		if defaultVal != "" {
			def.WriteString(fmt.Sprintf(" DEFAULT %s", defaultVal))
		}
	}

	return def.String()
}

// convertDataType 转换数据类型
func (m *Migrator) convertDataType(mysqlType string, maxLength *int) string {
	// 提取基础类型（去除长度等修饰符）
	baseType := strings.ToLower(mysqlType)
	if idx := strings.Index(baseType, "("); idx != -1 {
		baseType = baseType[:idx]
	}

	// 查找映射
	if pgType, exists := dataTypeMapping[baseType]; exists {
		// 对于需要长度的类型，添加长度信息
		if (baseType == "varchar" || baseType == "char") && maxLength != nil {
			return fmt.Sprintf("%s(%d)", pgType, *maxLength)
		}
		return pgType
	}

	// 如果没有找到映射，返回TEXT作为默认类型
	return "TEXT"
}

// convertDefaultValue 转换默认值
func (m *Migrator) convertDefaultValue(defaultVal, dataType string) string {
	if defaultVal == "NULL" {
		return "NULL"
	}

	// 处理特殊的MySQL默认值
	switch strings.ToUpper(defaultVal) {
	case "CURRENT_TIMESTAMP":
		return "CURRENT_TIMESTAMP"
	case "NOW()":
		return "CURRENT_TIMESTAMP"
	}

	// 对于字符串类型，确保有引号
	baseType := strings.ToLower(dataType)
	if strings.Contains(baseType, "char") || strings.Contains(baseType, "text") {
		if !strings.HasPrefix(defaultVal, "'") {
			return fmt.Sprintf("'%s'", defaultVal)
		}
	}

	return defaultVal
}

// migrateTableData 迁移表数据
func (m *Migrator) migrateTableData(tableInfo *TableInfo) (int64, error) {
	// 获取总记录数
	var totalCount int64
	err := m.mysqlDB.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableInfo.Name)).Scan(&totalCount)
	if err != nil {
		return 0, err
	}

	if totalCount == 0 {
		return 0, nil
	}

	fmt.Printf("  开始迁移 %d 条记录...\n", totalCount)

	// 构建INSERT语句，跳过自增ID字段
	var columnNames []string
	var placeholders []string
	var nonAutoIncrColumns []ColumnInfo
	placeholderIndex := 1

	for _, col := range tableInfo.Columns {
		if !col.IsAutoIncr { // 跳过自增字段
			columnNames = append(columnNames, col.Name)
			placeholders = append(placeholders, fmt.Sprintf("$%d", placeholderIndex))
			nonAutoIncrColumns = append(nonAutoIncrColumns, col)
			placeholderIndex++
		}
	}

	insertSQL := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableInfo.Name,
		strings.Join(columnNames, ", "),
		strings.Join(placeholders, ", "))

	// 准备INSERT语句
	stmt, err := m.postgresDB.Prepare(insertSQL)
	if err != nil {
		return 0, fmt.Errorf("准备INSERT语句失败: %v", err)
	}
	defer stmt.Close()

	// 一次性读取所有数据（跳过自增ID字段）
	selectSQL := fmt.Sprintf("SELECT %s FROM %s", strings.Join(columnNames, ", "), tableInfo.Name)
	rows, err := m.mysqlDB.Query(selectSQL)
	if err != nil {
		return 0, fmt.Errorf("查询数据失败: %v", err)
	}
	defer rows.Close()

	// 开始事务
	tx, err := m.postgresDB.Begin()
	if err != nil {
		return 0, fmt.Errorf("开始事务失败: %v", err)
	}

	var migratedCount int64
	for rows.Next() {
		// 创建扫描目标（只为非自增字段）
		values := make([]interface{}, len(nonAutoIncrColumns))
		scanArgs := make([]interface{}, len(nonAutoIncrColumns))
		for i := range values {
			scanArgs[i] = &values[i]
		}

		// 扫描数据
		if err := rows.Scan(scanArgs...); err != nil {
			tx.Rollback()
			return migratedCount, fmt.Errorf("扫描数据失败: %v", err)
		}

		// 转换数据
		convertedValues := m.convertValues(values, nonAutoIncrColumns)

		// 插入数据
		_, err := tx.Stmt(stmt).Exec(convertedValues...)
		if err != nil {
			tx.Rollback()
			return migratedCount, fmt.Errorf("插入数据失败: %v", err)
		}

		migratedCount++

		// 每1000条显示一次进度
		if migratedCount%1000 == 0 {
			progress := float64(migratedCount) / float64(totalCount) * 100
			fmt.Printf("  进度: %.1f%% (%d/%d)\n", progress, migratedCount, totalCount)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return migratedCount, fmt.Errorf("提交事务失败: %v", err)
	}

	// 显示最终进度
	fmt.Printf("  完成: 100.0%% (%d/%d)\n", migratedCount, totalCount)

	return migratedCount, nil
}

// convertValues 转换数据值
func (m *Migrator) convertValues(values []interface{}, columns []ColumnInfo) []interface{} {
	converted := make([]interface{}, len(values))

	for i, value := range values {
		if value == nil {
			converted[i] = nil
			continue
		}

		col := columns[i]
		baseType := strings.ToLower(col.DataType)

		switch baseType {
		case "tinyint":
			// MySQL的tinyint(1)通常用作布尔值
			if col.MaxLength != nil && *col.MaxLength == 1 {
				if v, ok := value.(int64); ok {
					converted[i] = v != 0
				} else {
					converted[i] = value
				}
			} else {
				converted[i] = value
			}
		case "datetime", "timestamp":
			// 确保时间格式正确
			if timeVal, ok := value.(time.Time); ok {
				converted[i] = timeVal
			} else if strVal, ok := value.(string); ok {
				if parsedTime, err := time.Parse("2006-01-02 15:04:05", strVal); err == nil {
					converted[i] = parsedTime
				} else {
					converted[i] = value
				}
			} else {
				converted[i] = value
			}
		case "json":
			// JSON数据保持原样
			converted[i] = value
		default:
			converted[i] = value
		}
	}

	return converted
}

// createIndexes 创建索引
func (m *Migrator) createIndexes(tableInfo *TableInfo) error {
	for _, index := range tableInfo.Indexes {
		var indexSQL string
		if index.IsUnique {
			indexSQL = fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS %s_%s ON %s (%s)",
				tableInfo.Name, index.Name, tableInfo.Name, strings.Join(index.Columns, ", "))
		} else {
			indexSQL = fmt.Sprintf("CREATE INDEX IF NOT EXISTS %s_%s ON %s (%s)",
				tableInfo.Name, index.Name, tableInfo.Name, strings.Join(index.Columns, ", "))
		}

		_, err := m.postgresDB.Exec(indexSQL)
		if err != nil {
			return fmt.Errorf("创建索引 %s 失败: %v", index.Name, err)
		}
	}

	return nil
}

// addError 添加错误信息
func (m *Migrator) addError(errMsg string) {
	m.report.Errors = append(m.report.Errors, errMsg)
	m.report.FailedTables = append(m.report.FailedTables, errMsg)
	log.Printf("错误: %s", errMsg)
}

// GenerateReport 生成迁移报告
func (m *Migrator) GenerateReport() {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("数据库迁移报告")
	fmt.Println(strings.Repeat("=", 60))

	duration := m.report.EndTime.Sub(m.report.StartTime)
	fmt.Printf("开始时间: %s\n", m.report.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("结束时间: %s\n", m.report.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("总耗时: %s\n", duration)
	fmt.Printf("总表数: %d\n", m.report.TotalTables)
	fmt.Printf("成功迁移: %d\n", m.report.SuccessTables)
	fmt.Printf("失败表数: %d\n", len(m.report.FailedTables))
	fmt.Printf("总记录数: %d\n", m.report.TotalRecords)

	if len(m.report.Errors) > 0 {
		fmt.Println("\n错误详情:")
		for i, err := range m.report.Errors {
			fmt.Printf("%d. %s\n", i+1, err)
		}
	}

	// 计算成功率
	successRate := float64(m.report.SuccessTables) / float64(m.report.TotalTables) * 100
	fmt.Printf("\n迁移成功率: %.1f%%\n", successRate)

	if successRate == 100 {
		fmt.Println("🎉 所有表迁移成功！")
	} else if successRate >= 80 {
		fmt.Println("⚠️  大部分表迁移成功，请检查失败的表")
	} else {
		fmt.Println("❌ 迁移存在较多问题，请检查错误信息")
	}

	// 保存报告到文件
	m.saveReportToFile()
}

// saveReportToFile 保存报告到文件
func (m *Migrator) saveReportToFile() {
	reportFile := fmt.Sprintf("migration_report_%s.json",
		time.Now().Format("20060102_150405"))

	reportData, err := json.MarshalIndent(m.report, "", "  ")
	if err != nil {
		log.Printf("生成报告JSON失败: %v", err)
		return
	}

	err = os.WriteFile(reportFile, reportData, 0644)
	if err != nil {
		log.Printf("保存报告文件失败: %v", err)
		return
	}

	fmt.Printf("详细报告已保存到: %s\n", reportFile)
}
