/*
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-08-24 17:43:56
 * @LastEditTime: 2025-08-24 17:44:16
 * @FilePath: /day1/test_converter.go
 */
package main

import (
	"fmt"
	"strings"
)

func main() {
	// 测试单引号转义功能
	testCases := []string{
		"INSERT INTO dog_words VALUES (1, '你说\\'nmsl我想这一定是有什么含义吧噢', NOW(), NOW());",
		"INSERT INTO dog_words VALUES (2, 'Hello\\'s world', NOW(), NOW());",
		"INSERT INTO dog_words VALUES (3, 'It\\'s a test', NOW(), NOW());",
		"INSERT INTO dog_words VALUES (4, 'Normal text', NOW(), NOW());",
	}

	converter := NewConverter("", "")

	fmt.Println("测试单引号转义修复:")
	fmt.Println(strings.Repeat("=", 50))

	for i, testCase := range testCases {
		fmt.Printf("测试用例 %d:\n", i+1)
		fmt.Printf("原始: %s\n", testCase)

		result := converter.convertInsertStatements(testCase)
		fmt.Printf("转换: %s\n", result)
		fmt.Println()
	}
}

// 简化的转换器结构，用于测试
type Converter struct {
	inputFile    string
	outputFile   string
	currentTable string
	indexes      []string
}

func NewConverter(inputFile, outputFile string) *Converter {
	return &Converter{
		inputFile:    inputFile,
		outputFile:   outputFile,
		currentTable: "",
		indexes:      make([]string, 0),
	}
}

func (c *Converter) convertInsertStatements(line string) string {
	if strings.HasPrefix(line, "INSERT INTO") {
		// 确保使用双引号而不是反引号
		line = strings.ReplaceAll(line, "`", "\"")

		// 修复单引号转义问题
		line = c.escapeSingleQuotes(line)
	}

	return line
}

func (c *Converter) escapeSingleQuotes(line string) string {
	// 简化的单引号转义方法
	// 将MySQL的\'转义转换为PostgreSQL的''转义

	// 先处理已经转义的单引号 \'
	line = strings.ReplaceAll(line, "\\'", "''")

	// 然后处理可能存在的其他单引号转义情况
	// 使用正则表达式更精确地处理VALUES子句中的内容
	if strings.Contains(line, "VALUES") {
		line = c.fixQuotesInValues(line)
	}

	return line
}

func (c *Converter) fixQuotesInValues(line string) string {
	// 找到VALUES部分并修复其中的引号问题
	valuesIndex := strings.Index(line, "VALUES")
	if valuesIndex == -1 {
		return line
	}

	prefix := line[:valuesIndex+6] // "VALUES"
	suffix := line[valuesIndex+6:]

	// 在VALUES部分中，将所有的\'替换为''
	// 这是一个更安全的方法，避免复杂的解析
	suffix = strings.ReplaceAll(suffix, "\\'", "''")

	// 处理可能存在的其他转义字符
	suffix = strings.ReplaceAll(suffix, "\\\"", "\"")
	suffix = strings.ReplaceAll(suffix, "\\\\", "\\")

	return prefix + suffix
}
